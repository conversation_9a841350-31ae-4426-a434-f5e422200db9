# Server configuration
PORT=3000
NODE_ENV=development
API_PREFIX=/api
CORS_ORIGIN=*

# Database configuration`
DB_HOST=localhost
DB_USER=sowmi
DB_PASSWORD=Jobs@1487
DB_NAME=plucon_services

# JWT configuration
JWT_SECRET=plucon_s3rv1c3s_jW7_S3cr3T_k3Y_2O25@&!pLuM3r1a
JWT_EXPIRES=24h
JWT_EXPIRES_IN=24h
JWT_REFRESH_SECRET=plucon_r3Fr3sh_s3cr3T_k3Y_2O25@&!pLuM3r1a
JWT_REFRESH_EXPIRES=7d
JWT_REFRESH_EXPIRES_IN=7d

# Admin API Key for Branding Management
ADMIN_API_KEY=pLuM3r1a_AdM1n_Br4nd1ng_K3Y_2O25@&!S3cur3
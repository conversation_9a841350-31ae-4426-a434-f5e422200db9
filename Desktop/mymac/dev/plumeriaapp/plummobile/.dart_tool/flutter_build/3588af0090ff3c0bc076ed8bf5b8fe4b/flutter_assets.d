 /Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/packages/cupertino_icons/assets/CupertinoIcons.ttf /Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/fonts/MaterialIcons-Regular.otf /Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/shaders/ink_sparkle.frag /Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/AssetManifest.json /Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/AssetManifest.bin /Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/FontManifest.json /Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/NOTICES.Z /Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/build/ios/Debug-iphonesimulator/App.framework/flutter_assets/NativeAssetsManifest.json:  /Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/pubspec.yaml /Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/ios/Runner/Info.plist /Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/ios/Flutter/AppFrameworkInfo.plist /Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/assets/CupertinoIcons.ttf /Users/<USER>/Desktop/mymac/development/flutter/bin/cache/artifacts/material_fonts/MaterialIcons-Regular.otf /Users/<USER>/Desktop/mymac/development/flutter/packages/flutter/lib/src/material/shaders/ink_sparkle.frag /Users/<USER>/Desktop/mymac/dev/plumeriaapp/plummobile/.dart_tool/flutter_build/3588af0090ff3c0bc076ed8bf5b8fe4b/native_assets.json /Users/<USER>/.pub-cache/hosted/pub.dev/async-2.12.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/boolean_selector-2.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/characters-1.4.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/clock-1.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/collection-1.19.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/cupertino_icons-1.0.8/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus-11.4.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/device_info_plus_platform_interface-7.0.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/dio-5.8.0+1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/dio_web_adapter-2.1.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/fake_async-1.3.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/ffi-2.1.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/file-7.0.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_lints-5.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage-9.2.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_linux-1.2.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_macos-3.1.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_platform_interface-1.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_web-1.2.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/flutter_secure_storage_windows-3.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/http-1.4.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/http_parser-4.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/js-0.6.7/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker-10.0.8/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_flutter_testing-3.0.9/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/leak_tracker_testing-3.0.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/lints-5.1.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/matcher-0.12.17/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/material_color_utilities-0.11.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/meta-1.16.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/nested-1.0.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path-1.9.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.17/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_platform_interface-2.1.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/platform-3.1.6/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/plugin_platform_interface-2.1.8/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/provider-6.1.5/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.5.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.10/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_platform_interface-2.4.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.3/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/source_span-1.10.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/stack_trace-1.12.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/stream_channel-2.1.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/string_scanner-1.4.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/term_glyph-1.2.2/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/test_api-0.7.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/typed_data-1.4.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/vector_math-2.1.4/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/vm_service-14.3.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/web-1.1.1/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/win32-5.13.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/win32_registry-2.1.0/LICENSE /Users/<USER>/.pub-cache/hosted/pub.dev/xdg_directories-1.1.0/LICENSE /Users/<USER>/Desktop/mymac/development/flutter/bin/cache/pkg/sky_engine/LICENSE /Users/<USER>/Desktop/mymac/development/flutter/packages/flutter/LICENSE
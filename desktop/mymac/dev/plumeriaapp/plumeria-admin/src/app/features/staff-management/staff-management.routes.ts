import { Routes } from '@angular/router';
import { StaffManagementComponent } from './staff-management.component';
import { StaffListComponent } from './staff-list/staff-list.component';
import { StaffFormComponent } from './staff-form/staff-form.component';
import { StaffDetailComponent } from './staff-detail/staff-detail.component';
import { AuthGuard } from '../../core/guards/auth.guard';
import { PermissionGuard } from '../../core/guards/permission.guard';

export const STAFF_MANAGEMENT_ROUTES: Routes = [
  {
    path: '',
    component: StaffManagementComponent,
    canActivate: [AuthGuard],
    children: [
      {
        path: '',
        component: StaffListComponent,
        canActivate: [AuthGuard], // Temporarily removed PermissionGuard for testing
        // data: { module: 'staff', action: 'read' },
      },
      {
        path: 'new',
        component: StaffFormComponent,
        canActivate: [AuthGuard], // Temporarily removed PermissionGuard for testing
        // data: { module: 'staff', action: 'create' },
      },
      {
        path: ':id',
        component: StaffDetailComponent,
        canActivate: [AuthGuard], // Temporarily removed PermissionGuard for testing
        // data: { module: 'staff', action: 'read' },
      },
      {
        path: 'edit/:id',
        component: StaffFormComponent,
        canActivate: [AuthGuard], // Temporarily removed PermissionGuard for testing
        // data: { module: 'staff', action: 'update' },
      },
    ],
  },
];

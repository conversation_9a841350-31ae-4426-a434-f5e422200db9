export interface BrandingConfig {
  id?: number;
  appName: string;
  companyName: string;
  tagline?: string;
  primaryColor: string;
  secondaryColor: string;
  accentColor: string;
  errorColor: string;
  backgroundColor: string;
  logoUrl?: string;
  appIconUrl?: string;
  splashImageUrl?: string;
  loginWelcome?: string;
  loginSubtitle?: string;
  dashboardWelcome?: string;
  dashboardSubtitle?: string;
  showRegistration: boolean;
  enableSocialLogin: boolean;
  enableBiometric: boolean;
  showForgotPassword: boolean;
  version: number;
  isActive: boolean;
  createdAt?: string;
  updatedAt?: string;
}

export interface BrandingTheme {
  primaryColor: string;
  secondaryColor: string;
  accentColor: string;
  errorColor: string;
  backgroundColor: string;
}

export interface BrandingTexts {
  loginWelcome?: string;
  loginSubtitle?: string;
  dashboardWelcome?: string;
  dashboardSubtitle?: string;
}

export interface BrandingAssets {
  logoUrl?: string;
  appIconUrl?: string;
  splashImageUrl?: string;
}

export interface BrandingFeatures {
  showRegistration: boolean;
  enableSocialLogin: boolean;
  enableBiometric: boolean;
  showForgotPassword: boolean;
}

export interface BrandingVersion {
  version: number;
  lastUpdated: string;
}

export interface BrandingUpdateRequest {
  appName: string;
  companyName: string;
  tagline?: string;
  theme: BrandingTheme;
  texts?: BrandingTexts;
  assets?: BrandingAssets;
  features?: BrandingFeatures;
}

export interface BrandingResponse {
  success: boolean;
  message: string;
  data?: BrandingConfig;
  timestamp: string;
}

export interface BrandingListResponse {
  success: boolean;
  message: string;
  data?: BrandingConfig[];
  timestamp: string;
}

// Default branding configuration
export const DEFAULT_BRANDING: Partial<BrandingConfig> = {
  appName: 'Plumeria Staff',
  companyName: 'Plumeria Construction',
  tagline: 'Construction Management Made Simple',
  primaryColor: '#667eea',
  secondaryColor: '#764ba2',
  accentColor: '#4CAF50',
  errorColor: '#FF5722',
  backgroundColor: '#F8FAFC',
  loginWelcome: 'Welcome to\nPlumeria Staff',
  loginSubtitle: 'Construction Management Made Simple',
  dashboardWelcome: 'Welcome back,',
  dashboardSubtitle: 'Have a productive day!',
  showRegistration: false,
  enableSocialLogin: false,
  enableBiometric: true,
  showForgotPassword: true,
  version: 1,
  isActive: true
};

// Color palette options for easy selection
export const COLOR_PALETTE = {
  primary: [
    '#667eea', // Default blue
    '#2196F3', // Material Blue
    '#3F51B5', // Indigo
    '#9C27B0', // Purple
    '#E91E63', // Pink
    '#F44336', // Red
    '#FF9800', // Orange
    '#FF5722', // Deep Orange
    '#4CAF50', // Green
    '#009688', // Teal
    '#00BCD4', // Cyan
    '#795548', // Brown
    '#607D8B'  // Blue Grey
  ],
  secondary: [
    '#764ba2', // Default purple
    '#1976D2', // Dark Blue
    '#303F9F', // Dark Indigo
    '#7B1FA2', // Dark Purple
    '#C2185B', // Dark Pink
    '#D32F2F', // Dark Red
    '#F57C00', // Dark Orange
    '#E64A19', // Dark Deep Orange
    '#388E3C', // Dark Green
    '#00796B', // Dark Teal
    '#0097A7', // Dark Cyan
    '#5D4037', // Dark Brown
    '#455A64'  // Dark Blue Grey
  ]
};

// Validation rules
export const BRANDING_VALIDATION = {
  appName: {
    required: true,
    minLength: 2,
    maxLength: 100
  },
  companyName: {
    required: true,
    minLength: 2,
    maxLength: 255
  },
  tagline: {
    required: false,
    maxLength: 500
  },
  colors: {
    pattern: /^#[0-9A-Fa-f]{6}$/
  },
  texts: {
    loginWelcome: { maxLength: 200 },
    loginSubtitle: { maxLength: 200 },
    dashboardWelcome: { maxLength: 200 },
    dashboardSubtitle: { maxLength: 200 }
  }
};

import 'dart:convert';
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/auth_model.dart';
import '../models/staff_model.dart';

class StorageService {
  static const FlutterSecureStorage _secureStorage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );

  // Secure storage keys
  static const String _accessTokenKey = 'access_token';
  static const String _refreshTokenKey = 'refresh_token';
  static const String _tokenTypeKey = 'token_type';
  static const String _expiresInKey = 'expires_in';

  // Shared preferences keys
  static const String _staffDataKey = 'staff_data';
  static const String _isFirstLaunchKey = 'is_first_launch';
  static const String _biometricEnabledKey = 'biometric_enabled';
  static const String _rememberMeKey = 'remember_me';
  static const String _lastMobileKey = 'last_mobile';

  // Auth tokens management
  Future<void> saveAuthTokens(AuthTokens tokens) async {
    await Future.wait([
      _secureStorage.write(key: _accessTokenKey, value: tokens.accessToken),
      _secureStorage.write(key: _refreshTokenKey, value: tokens.refreshToken),
      _secureStorage.write(key: _tokenTypeKey, value: tokens.tokenType),
      _secureStorage.write(key: _expiresInKey, value: tokens.expiresIn),
    ]);
  }

  Future<AuthTokens?> getAuthTokens() async {
    try {
      final accessToken = await _secureStorage.read(key: _accessTokenKey);
      final refreshToken = await _secureStorage.read(key: _refreshTokenKey);
      final tokenType = await _secureStorage.read(key: _tokenTypeKey);
      final expiresIn = await _secureStorage.read(key: _expiresInKey);

      if (accessToken != null && refreshToken != null) {
        return AuthTokens(
          accessToken: accessToken,
          refreshToken: refreshToken,
          tokenType: tokenType ?? 'Bearer',
          expiresIn: expiresIn ?? '24h',
        );
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  Future<void> clearAuthTokens() async {
    await Future.wait([
      _secureStorage.delete(key: _accessTokenKey),
      _secureStorage.delete(key: _refreshTokenKey),
      _secureStorage.delete(key: _tokenTypeKey),
      _secureStorage.delete(key: _expiresInKey),
    ]);
  }

  // Staff data management
  Future<void> saveStaffData(Staff staff) async {
    final prefs = await SharedPreferences.getInstance();
    final staffJson = jsonEncode(staff.toJson());
    await prefs.setString(_staffDataKey, staffJson);
  }

  Future<Staff?> getStaffData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final staffJson = prefs.getString(_staffDataKey);
      
      if (staffJson != null) {
        final staffMap = jsonDecode(staffJson);
        return Staff.fromJson(staffMap);
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  Future<void> clearStaffData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_staffDataKey);
  }

  // App preferences
  Future<void> setFirstLaunch(bool isFirstLaunch) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_isFirstLaunchKey, isFirstLaunch);
  }

  Future<bool> isFirstLaunch() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_isFirstLaunchKey) ?? true;
  }

  Future<void> setBiometricEnabled(bool enabled) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_biometricEnabledKey, enabled);
  }

  Future<bool> isBiometricEnabled() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_biometricEnabledKey) ?? false;
  }

  Future<void> setRememberMe(bool remember) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool(_rememberMeKey, remember);
  }

  Future<bool> getRememberMe() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_rememberMeKey) ?? false;
  }

  Future<void> setLastMobile(String mobile) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_lastMobileKey, mobile);
  }

  Future<String?> getLastMobile() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_lastMobileKey);
  }

  Future<void> clearLastMobile() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_lastMobileKey);
  }

  // Clear all auth-related data
  Future<void> clearAuthData() async {
    await Future.wait([
      clearAuthTokens(),
      clearStaffData(),
      clearLastMobile(),
    ]);
  }

  // Clear all app data
  Future<void> clearAllData() async {
    await Future.wait([
      _secureStorage.deleteAll(),
      SharedPreferences.getInstance().then((prefs) => prefs.clear()),
    ]);
  }

  // Check if user is logged in
  Future<bool> isLoggedIn() async {
    final tokens = await getAuthTokens();
    return tokens != null;
  }
}
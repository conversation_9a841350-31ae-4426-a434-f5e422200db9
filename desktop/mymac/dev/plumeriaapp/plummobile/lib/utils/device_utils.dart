import 'dart:io';
import 'package:flutter/services.dart';
import 'package:device_info_plus/device_info_plus.dart';
import '../models/auth_model.dart';

class DeviceUtils {
  static final DeviceInfoPlugin _deviceInfo = DeviceInfoPlugin();

  static Future<DeviceInfo> getDeviceInfo() async {
    try {
      String deviceId = '';
      String deviceName = '';
      String deviceModel = '';
      String osVersion = '';
      String platform = '';

      if (Platform.isAndroid) {
        platform = 'android';
        final androidInfo = await _deviceInfo.androidInfo;
        deviceId = androidInfo.id;
        deviceName = '${androidInfo.brand} ${androidInfo.model}';
        deviceModel = androidInfo.model;
        osVersion = 'Android ${androidInfo.version.release}';
      } else if (Platform.isIOS) {
        platform = 'ios';
        final iosInfo = await _deviceInfo.iosInfo;
        deviceId = iosInfo.identifierForVendor ?? '';
        deviceName = '${iosInfo.name}';
        deviceModel = iosInfo.model;
        osVersion = 'iOS ${iosInfo.systemVersion}';
      } else {
        platform = Platform.operatingSystem;
        deviceId = 'unknown_${DateTime.now().millisecondsSinceEpoch}';
        deviceName = 'Unknown Device';
        deviceModel = 'Unknown';
        osVersion = 'Unknown';
      }

      return DeviceInfo(
        deviceId: deviceId,
        deviceName: deviceName,
        platform: platform,
        deviceModel: deviceModel,
        osVersion: osVersion,
        appVersion: '1.0.0',
      );
    } catch (e) {
      // Fallback device info
      return DeviceInfo(
        deviceId: 'unknown_${DateTime.now().millisecondsSinceEpoch}',
        deviceName: 'Unknown Device',
        platform: Platform.operatingSystem,
        deviceModel: 'Unknown',
        osVersion: 'Unknown',
        appVersion: '1.0.0',
      );
    }
  }

  static Future<String> getDeviceId() async {
    final deviceInfo = await getDeviceInfo();
    return deviceInfo.deviceId;
  }

  static String getPlatform() {
    return Platform.operatingSystem;
  }

  static bool get isAndroid => Platform.isAndroid;
  static bool get isIOS => Platform.isIOS;

  static Future<Map<String, String>> getDeviceHeaders() async {
    final deviceInfo = await getDeviceInfo();
    return {
      'X-Device-ID': deviceInfo.deviceId,
      'X-Device-Name': deviceInfo.deviceName,
      'X-Platform': deviceInfo.platform,
      'X-Device-Model': deviceInfo.deviceModel ?? '',
      'X-OS-Version': deviceInfo.osVersion ?? '',
      'X-App-Version': deviceInfo.appVersion,
    };
  }
}
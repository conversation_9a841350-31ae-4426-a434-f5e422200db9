import 'staff_model.dart';

class AuthTokens {
  final String accessToken;
  final String refreshToken;
  final String tokenType;
  final String expiresIn;

  AuthTokens({
    required this.accessToken,
    required this.refreshToken,
    required this.tokenType,
    required this.expiresIn,
  });

  factory AuthTokens.fromJson(Map<String, dynamic> json) {
    return AuthTokens(
      accessToken: json['access_token'],
      refreshToken: json['refresh_token'],
      tokenType: json['token_type'] ?? 'Bearer',
      expiresIn: json['expires_in'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'access_token': accessToken,
      'refresh_token': refreshToken,
      'token_type': tokenType,
      'expires_in': expiresIn,
    };
  }
}

class LoginResponse {
  final bool success;
  final String message;
  final Staff? staff;
  final AuthTokens? tokens;

  LoginResponse({
    required this.success,
    required this.message,
    this.staff,
    this.tokens,
  });

  factory LoginResponse.fromJson(Map<String, dynamic> json) {
    return LoginResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      staff: json['data']?['staff'] != null
          ? Staff.fromJson(json['data']['staff'])
          : null,
      tokens: json['data']?['tokens'] != null
          ? AuthTokens.fromJson(json['data']['tokens'])
          : null,
    );
  }
}

class ApiResponse {
  final bool success;
  final String message;
  final dynamic data;
  final int statusCode;

  ApiResponse({
    required this.success,
    required this.message,
    this.data,
    required this.statusCode,
  });

  factory ApiResponse.fromJson(Map<String, dynamic> json, int statusCode) {
    return ApiResponse(
      success: json['success'] ?? false,
      message: json['message'] ?? '',
      data: json['data'],
      statusCode: statusCode,
    );
  }
}

class DeviceInfo {
  final String deviceId;
  final String deviceName;
  final String platform;
  final String? deviceModel;
  final String? osVersion;
  final String appVersion;
  final String? pushToken;

  DeviceInfo({
    required this.deviceId,
    required this.deviceName,
    required this.platform,
    this.deviceModel,
    this.osVersion,
    required this.appVersion,
    this.pushToken,
  });

  Map<String, dynamic> toJson() {
    return {
      'device_id': deviceId,
      'device_name': deviceName,
      'platform': platform,
      'device_model': deviceModel,
      'os_version': osVersion,
      'app_version': appVersion,
      'push_token': pushToken,
    };
  }
}
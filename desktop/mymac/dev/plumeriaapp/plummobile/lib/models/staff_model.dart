class Staff {
  final int id;
  final String name;
  final String mobile;
  final String? email;
  final String? profilePicture;
  final Department? department;
  final Designation? designation;
  final EmergencyContact? emergencyContact;
  final Address? address;
  final BankDetails? bankDetails;
  final bool isActive;
  final DateTime? lastLogin;

  Staff({
    required this.id,
    required this.name,
    required this.mobile,
    this.email,
    this.profilePicture,
    this.department,
    this.designation,
    this.emergencyContact,
    this.address,
    this.bankDetails,
    required this.isActive,
    this.lastLogin,
  });

  factory Staff.fromJson(Map<String, dynamic> json) {
    return Staff(
      id: json['id'],
      name: json['name'],
      mobile: json['mobile'],
      email: json['email'],
      profilePicture: json['profile_picture'],
      department: json['department'] != null
          ? Department.fromJson(json['department'])
          : null,
      designation: json['designation'] != null
          ? Designation.fromJson(json['designation'])
          : null,
      emergencyContact: json['emergency_contact'] != null
          ? EmergencyContact.fromJson(json['emergency_contact'])
          : null,
      address: json['address'] != null
          ? Address.fromJson(json['address'])
          : null,
      bankDetails: json['bank_details'] != null
          ? BankDetails.fromJson(json['bank_details'])
          : null,
      isActive: json['is_active'] ?? true,
      lastLogin: json['last_login'] != null
          ? DateTime.parse(json['last_login'])
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'mobile': mobile,
      'email': email,
      'profile_picture': profilePicture,
      'department': department?.toJson(),
      'designation': designation?.toJson(),
      'emergency_contact': emergencyContact?.toJson(),
      'address': address?.toJson(),
      'bank_details': bankDetails?.toJson(),
      'is_active': isActive,
      'last_login': lastLogin?.toIso8601String(),
    };
  }
}

class Department {
  final int id;
  final String name;

  Department({
    required this.id,
    required this.name,
  });

  factory Department.fromJson(Map<String, dynamic> json) {
    return Department(
      id: json['id'],
      name: json['name'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
    };
  }
}

class Designation {
  final int id;
  final String name;

  Designation({
    required this.id,
    required this.name,
  });

  factory Designation.fromJson(Map<String, dynamic> json) {
    return Designation(
      id: json['id'],
      name: json['name'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
    };
  }
}

class EmergencyContact {
  final String? name;
  final String? phone;
  final String? relation;

  EmergencyContact({
    this.name,
    this.phone,
    this.relation,
  });

  factory EmergencyContact.fromJson(Map<String, dynamic> json) {
    return EmergencyContact(
      name: json['name'],
      phone: json['phone'],
      relation: json['relation'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'phone': phone,
      'relation': relation,
    };
  }
}

class Address {
  final String? street;
  final int? cityId;
  final String? cityName;
  final int? stateId;
  final String? stateName;
  final String? pincode;

  Address({
    this.street,
    this.cityId,
    this.cityName,
    this.stateId,
    this.stateName,
    this.pincode,
  });

  factory Address.fromJson(Map<String, dynamic> json) {
    return Address(
      street: json['street'],
      cityId: json['city_id'],
      cityName: json['city_name'],
      stateId: json['state_id'],
      stateName: json['state_name'],
      pincode: json['pincode'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'street': street,
      'city_id': cityId,
      'city_name': cityName,
      'state_id': stateId,
      'state_name': stateName,
      'pincode': pincode,
    };
  }
}

class BankDetails {
  final String? bankName;
  final String? accountNumber;
  final String? ifscCode;

  BankDetails({
    this.bankName,
    this.accountNumber,
    this.ifscCode,
  });

  factory BankDetails.fromJson(Map<String, dynamic> json) {
    return BankDetails(
      bankName: json['bank_name'],
      accountNumber: json['account_number'],
      ifscCode: json['ifsc_code'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'bank_name': bankName,
      'account_number': accountNumber,
      'ifsc_code': ifscCode,
    };
  }
}
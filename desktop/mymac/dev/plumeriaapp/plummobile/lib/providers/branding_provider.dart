import 'package:flutter/material.dart';
import '../models/branding_model.dart';
import '../services/branding_service.dart';

class BrandingProvider extends ChangeNotifier {
  BrandingConfig? _branding;
  bool _isLoading = false;
  String? _error;

  // Getters
  BrandingConfig? get branding => _branding;
  bool get isLoading => _isLoading;
  String? get error => _error;
  bool get isInitialized => _branding != null;

  // Get branding with fallback to default
  BrandingConfig get brandingOrDefault => _branding ?? BrandingConfig.defaultConfig();

  /// Initialize branding (call this at app startup)
  Future<void> initialize() async {
    if (_branding != null) return; // Already initialized
    
    _setLoading(true);
    _clearError();

    try {
      _branding = await BrandingService.getBranding();
      notifyListeners();
    } catch (e) {
      _setError('Failed to load branding configuration');
      _branding = BrandingConfig.defaultConfig(); // Fallback to default
      notifyListeners();
    } finally {
      _setLoading(false);
    }
  }

  /// Refresh branding from API
  Future<void> refresh() async {
    _setLoading(true);
    _clearError();

    try {
      _branding = await BrandingService.refreshBranding();
      notifyListeners();
    } catch (e) {
      _setError('Failed to refresh branding configuration');
    } finally {
      _setLoading(false);
    }
  }

  /// Check for branding updates
  Future<bool> checkForUpdates() async {
    try {
      final version = await BrandingService.checkVersion();
      if (version != null && _branding != null) {
        return version.version != _branding!.version;
      }
      return false;
    } catch (e) {
      print('Error checking for branding updates: $e');
      return false;
    }
  }

  /// Update branding configuration (Admin only)
  Future<bool> updateBranding(BrandingConfig newBranding, String adminKey) async {
    _setLoading(true);
    _clearError();

    try {
      final success = await BrandingService.updateBranding(newBranding, adminKey);
      if (success) {
        _branding = newBranding;
        notifyListeners();
        return true;
      } else {
        _setError('Failed to update branding configuration');
        return false;
      }
    } catch (e) {
      _setError('Error updating branding: $e');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  /// Generate Flutter ThemeData from branding configuration
  ThemeData generateThemeData() {
    final config = brandingOrDefault;
    
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.light,
      primaryColor: config.theme.primaryColor,
      scaffoldBackgroundColor: config.theme.backgroundColor,
      colorScheme: ColorScheme.fromSeed(
        seedColor: config.theme.primaryColor,
        brightness: Brightness.light,
        primary: config.theme.primaryColor,
        secondary: config.theme.secondaryColor,
        error: config.theme.errorColor,
        surface: config.theme.backgroundColor,
      ),
      appBarTheme: AppBarTheme(
        backgroundColor: config.theme.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: false,
        titleTextStyle: const TextStyle(
          fontSize: 22,
          fontWeight: FontWeight.bold,
          color: Colors.white,
          letterSpacing: -0.5,
        ),
        iconTheme: const IconThemeData(color: Colors.white, size: 24),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: config.theme.primaryColor,
          foregroundColor: Colors.white,
          elevation: 0,
          shadowColor: Colors.transparent,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
          textStyle: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.w600,
            letterSpacing: 0.5,
          ),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: const BorderRadius.all(Radius.circular(16)),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: const BorderRadius.all(Radius.circular(16)),
          borderSide: BorderSide(color: Colors.grey.shade300),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: const BorderRadius.all(Radius.circular(16)),
          borderSide: BorderSide(color: config.theme.primaryColor, width: 2),
        ),
        filled: true,
        fillColor: Colors.grey.shade50,
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 20,
          vertical: 16,
        ),
      ),
      cardTheme: CardTheme(
        elevation: 0,
        color: Colors.white,
        shadowColor: Colors.black.withValues(alpha: 0.05),
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.all(Radius.circular(20)),
        ),
        margin: const EdgeInsets.symmetric(vertical: 8),
      ),
    );
  }

  /// Generate dark theme
  ThemeData generateDarkThemeData() {
    final config = brandingOrDefault;
    
    return ThemeData(
      useMaterial3: true,
      brightness: Brightness.dark,
      primaryColor: config.theme.primaryColor,
      scaffoldBackgroundColor: const Color(0xFF121212),
      colorScheme: ColorScheme.fromSeed(
        seedColor: config.theme.primaryColor,
        brightness: Brightness.dark,
        primary: config.theme.primaryColor,
        secondary: config.theme.secondaryColor,
        error: config.theme.errorColor,
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: Color(0xFF1E1E1E),
        foregroundColor: Colors.white,
        elevation: 0,
        titleTextStyle: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.w600,
          color: Colors.white,
        ),
      ),
    );
  }

  /// Get app title from branding
  String get appTitle => brandingOrDefault.appName;

  /// Get company name from branding
  String get companyName => brandingOrDefault.companyName;

  /// Get tagline from branding
  String get tagline => brandingOrDefault.tagline ?? '';

  // Private helper methods
  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
  }

  /// Dispose method
  @override
  void dispose() {
    super.dispose();
  }
}

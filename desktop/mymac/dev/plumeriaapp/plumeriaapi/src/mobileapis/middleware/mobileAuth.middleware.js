const jwt = require('jsonwebtoken');
const Staff = require('../../models/staff.model');
const { mobileResponse } = require('../utils/response.util');

/**
 * Mobile Authentication Middleware
 * Verifies JWT tokens specifically for mobile staff access
 */
const mobileAuthMiddleware = async (req, res, next) => {
  try {
    // Get token from header
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return mobileResponse.authError(res, 'Access token is required');
    }

    const token = authHeader.substring(7); // Remove 'Bearer ' prefix

    // Verify token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // Check if it's a mobile staff token
    if (decoded.type !== 'mobile_staff') {
      return mobileResponse.authError(res, 'Invalid token type');
    }

    // Get staff details
    const staff = await Staff.findById(decoded.staffId);
    
    if (!staff) {
      return mobileResponse.authError(res, 'Staff member not found');
    }

    if (!staff.is_active) {
      return mobileResponse.authError(res, 'Account has been deactivated');
    }

    // Add staff info to request object (mobile-optimized)
    req.staff = {
      id: staff.id,
      mobile: staff.staff_mobile,
      name: staff.staff_name,
      email: staff.staff_email,
      department_id: staff.department_id,
      department_name: staff.department_name,
      designation_id: staff.designation_id,
      designation_name: staff.designation_name,
      profile_picture: staff.profile_picture
    };

    next();

  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return mobileResponse.authError(res, 'Invalid token');
    }
    
    if (error.name === 'TokenExpiredError') {
      return mobileResponse.authError(res, 'Token has expired');
    }

    console.error('Mobile auth middleware error:', error);
    return mobileResponse.serverError(res, 'Authentication error');
  }
};

/**
 * Optional mobile authentication middleware
 * Adds staff info if token is valid, but doesn't require authentication
 */
const optionalMobileAuth = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return next(); // Continue without authentication
    }

    const token = authHeader.substring(7);
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    if (decoded.type === 'mobile_staff') {
      const staff = await Staff.findById(decoded.staffId);
      
      if (staff && staff.is_active) {
        req.staff = {
          id: staff.id,
          mobile: staff.staff_mobile,
          name: staff.staff_name,
          email: staff.staff_email,
          department_id: staff.department_id,
          department_name: staff.department_name,
          designation_id: staff.designation_id,
          designation_name: staff.designation_name,
          profile_picture: staff.profile_picture
        };
      }
    }

    next();

  } catch (error) {
    // Ignore errors and continue without authentication
    next();
  }
};

/**
 * Rate limiting middleware for mobile APIs
 * Limits requests per IP address
 */
const rateLimitMiddleware = (maxRequests = 100, windowMs = 60 * 60 * 1000) => {
  const requests = new Map();

  return (req, res, next) => {
    const ip = req.ip || req.connection.remoteAddress;
    const now = Date.now();
    const windowStart = now - windowMs;

    // Clean old entries
    for (const [key, timestamps] of requests.entries()) {
      const filtered = timestamps.filter(time => time > windowStart);
      if (filtered.length === 0) {
        requests.delete(key);
      } else {
        requests.set(key, filtered);
      }
    }

    // Get current requests for this IP
    const ipRequests = requests.get(ip) || [];
    const recentRequests = ipRequests.filter(time => time > windowStart);

    if (recentRequests.length >= maxRequests) {
      return mobileResponse.error(res, 'Too many requests. Please try again later.', 429);
    }

    // Add current request
    recentRequests.push(now);
    requests.set(ip, recentRequests);

    next();
  };
};

/**
 * Device information middleware
 * Captures device information from headers
 */
const deviceInfoMiddleware = (req, res, next) => {
  // Extract device information from headers
  req.deviceInfo = {
    userAgent: req.headers['user-agent'] || '',
    platform: req.headers['x-platform'] || 'unknown',
    appVersion: req.headers['x-app-version'] || '1.0.0',
    deviceId: req.headers['x-device-id'] || '',
    osVersion: req.headers['x-os-version'] || '',
    deviceModel: req.headers['x-device-model'] || '',
    pushToken: req.headers['x-push-token'] || ''
  };

  next();
};

/**
 * API version check middleware
 * Ensures mobile app is using compatible API version
 */
const apiVersionMiddleware = (minVersion = '1.0.0') => {
  return (req, res, next) => {
    const clientVersion = req.headers['x-app-version'] || '1.0.0';
    
    // Simple version comparison (you can implement more sophisticated logic)
    const isVersionSupported = compareVersions(clientVersion, minVersion) >= 0;
    
    if (!isVersionSupported) {
      return mobileResponse.error(res, 'App version not supported. Please update your app.', 426, {
        required_version: minVersion,
        current_version: clientVersion
      });
    }

    next();
  };
};

/**
 * Input validation middleware
 * Validates common request inputs
 */
const validateMobileInput = (req, res, next) => {
  // Trim string inputs
  if (req.body) {
    Object.keys(req.body).forEach(key => {
      if (typeof req.body[key] === 'string') {
        req.body[key] = req.body[key].trim();
      }
    });
  }

  // Validate mobile number format if present
  if (req.body.staff_mobile) {
    const mobileRegex = /^[6-9]\d{9}$/;
    if (!mobileRegex.test(req.body.staff_mobile)) {
      return mobileResponse.validationError(res, {
        staff_mobile: 'Invalid mobile number format'
      });
    }
  }

  next();
};

/**
 * Compare two version strings
 * @param {string} version1 
 * @param {string} version2 
 * @returns {number} -1 if version1 < version2, 0 if equal, 1 if version1 > version2
 */
function compareVersions(version1, version2) {
  const v1parts = version1.split('.').map(Number);
  const v2parts = version2.split('.').map(Number);
  
  const maxLength = Math.max(v1parts.length, v2parts.length);
  
  for (let i = 0; i < maxLength; i++) {
    const v1part = v1parts[i] || 0;
    const v2part = v2parts[i] || 0;
    
    if (v1part < v2part) return -1;
    if (v1part > v2part) return 1;
  }
  
  return 0;
}

module.exports = {
  mobileAuthMiddleware,
  optionalMobileAuth,
  rateLimitMiddleware,
  deviceInfoMiddleware,
  apiVersionMiddleware,
  validateMobileInput
};
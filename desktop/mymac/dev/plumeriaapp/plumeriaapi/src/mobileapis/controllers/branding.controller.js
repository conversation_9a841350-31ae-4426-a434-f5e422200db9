const db = require('../../config/database');
const ResponseUtil = require('../utils/response.util');

class BrandingController {
  /**
   * Get current branding configuration
   * GET /api/mobile/v1/branding
   */
  static async getBranding(req, res) {
    try {
      const query = `
        SELECT 
          app_name,
          company_name,
          tagline,
          primary_color,
          secondary_color,
          accent_color,
          error_color,
          background_color,
          logo_url,
          app_icon_url,
          splash_image_url,
          login_welcome,
          login_subtitle,
          dashboard_welcome,
          dashboard_subtitle,
          show_registration,
          enable_social_login,
          enable_biometric,
          show_forgot_password,
          version,
          updated_at
        FROM app_branding 
        WHERE is_active = TRUE 
        ORDER BY id DESC 
        LIMIT 1
      `;

      const rows = await db.query(query);

      if (rows.length === 0) {
        return ResponseUtil.error(res, 'No branding configuration found', 404);
      }

      const branding = rows[0];
      
      // Format response according to mobile app expectations
      const response = {
        appName: branding.app_name,
        companyName: branding.company_name,
        tagline: branding.tagline,
        theme: {
          primaryColor: branding.primary_color,
          secondaryColor: branding.secondary_color,
          accentColor: branding.accent_color,
          errorColor: branding.error_color,
          backgroundColor: branding.background_color
        },
        assets: {
          logoUrl: branding.logo_url,
          appIconUrl: branding.app_icon_url,
          splashImageUrl: branding.splash_image_url
        },
        texts: {
          loginWelcome: branding.login_welcome,
          loginSubtitle: branding.login_subtitle,
          dashboardWelcome: branding.dashboard_welcome,
          dashboardSubtitle: branding.dashboard_subtitle
        },
        features: {
          showRegistration: Boolean(branding.show_registration),
          enableSocialLogin: Boolean(branding.enable_social_login),
          enableBiometric: Boolean(branding.enable_biometric),
          showForgotPassword: Boolean(branding.show_forgot_password)
        },
        version: branding.version,
        lastUpdated: branding.updated_at
      };

      return ResponseUtil.success(res, response, 'Branding configuration retrieved successfully');
    } catch (error) {
      console.error('Error fetching branding:', error);
      return ResponseUtil.error(res, 'Failed to fetch branding configuration', 500);
    }
  }

  /**
   * Check branding version for cache invalidation
   * GET /api/mobile/v1/branding/version
   */
  static async checkVersion(req, res) {
    try {
      const query = `
        SELECT version, updated_at 
        FROM app_branding 
        WHERE is_active = TRUE 
        LIMIT 1
      `;

      const rows = await db.query(query);

      if (rows.length === 0) {
        return ResponseUtil.error(res, 'No active branding found', 404);
      }

      const data = {
        version: rows[0].version,
        lastUpdated: rows[0].updated_at
      };

      return ResponseUtil.success(res, data, 'Branding version retrieved successfully');
    } catch (error) {
      console.error('Error checking branding version:', error);
      return ResponseUtil.error(res, 'Failed to check branding version', 500);
    }
  }

  /**
   * Update branding configuration (Admin only)
   * PUT /api/mobile/v1/branding
   */
  static async updateBranding(req, res) {
    try {
      const {
        appName,
        companyName,
        tagline,
        theme,
        texts,
        features,
        assets
      } = req.body;

      // Validate required fields
      if (!appName || !companyName) {
        return ResponseUtil.error(res, 'App name and company name are required', 400);
      }

      // Start transaction
      const connection = await db.pool.getConnection();
      await connection.beginTransaction();

      try {
        // Deactivate current branding
        await connection.execute(
          'UPDATE app_branding SET is_active = FALSE WHERE is_active = TRUE'
        );

        // Get next version number
        const [versionRows] = await connection.execute(
          'SELECT MAX(version) as max_version FROM app_branding'
        );
        const nextVersion = (versionRows[0].max_version || 0) + 1;

        // Insert new branding
        const [result] = await connection.execute(
          `INSERT INTO app_branding (
            app_name, company_name, tagline,
            primary_color, secondary_color, accent_color, error_color, background_color,
            logo_url, app_icon_url, splash_image_url,
            login_welcome, login_subtitle, dashboard_welcome, dashboard_subtitle,
            show_registration, enable_social_login, enable_biometric, show_forgot_password,
            version, is_active
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            appName,
            companyName,
            tagline || null,
            theme?.primaryColor || '#667eea',
            theme?.secondaryColor || '#764ba2',
            theme?.accentColor || '#4CAF50',
            theme?.errorColor || '#FF5722',
            theme?.backgroundColor || '#F8FAFC',
            assets?.logoUrl || null,
            assets?.appIconUrl || null,
            assets?.splashImageUrl || null,
            texts?.loginWelcome || null,
            texts?.loginSubtitle || null,
            texts?.dashboardWelcome || null,
            texts?.dashboardSubtitle || null,
            features?.showRegistration || false,
            features?.enableSocialLogin || false,
            features?.enableBiometric !== undefined ? features.enableBiometric : true,
            features?.showForgotPassword !== undefined ? features.showForgotPassword : true,
            nextVersion,
            true
          ]
        );

        await connection.commit();
        connection.release();

        const responseData = {
          id: result.insertId,
          version: nextVersion,
          message: 'Branding updated successfully'
        };

        return ResponseUtil.success(res, responseData, 'Branding configuration updated successfully');
      } catch (error) {
        await connection.rollback();
        connection.release();
        throw error;
      }
    } catch (error) {
      console.error('Error updating branding:', error);
      return ResponseUtil.error(res, 'Failed to update branding configuration', 500);
    }
  }

  /**
   * Reset branding to default configuration (Admin only)
   * POST /api/mobile/v1/branding/reset
   */
  static async resetToDefault(req, res) {
    try {
      // Start transaction
      const connection = await db.pool.getConnection();
      await connection.beginTransaction();

      try {
        // Deactivate current branding
        await connection.execute(
          'UPDATE app_branding SET is_active = FALSE WHERE is_active = TRUE'
        );

        // Get next version number
        const [versionRows] = await connection.execute(
          'SELECT MAX(version) as max_version FROM app_branding'
        );
        const nextVersion = (versionRows[0].max_version || 0) + 1;

        // Insert default branding
        const [result] = await connection.execute(
          `INSERT INTO app_branding (
            app_name, company_name, tagline,
            primary_color, secondary_color, accent_color, error_color, background_color,
            logo_url, app_icon_url, splash_image_url,
            login_welcome, login_subtitle, dashboard_welcome, dashboard_subtitle,
            show_registration, enable_social_login, enable_biometric, show_forgot_password,
            version, is_active
          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            'Plumeria Staff',
            'Plumeria Construction',
            'Construction Management Made Simple',
            '#667eea',
            '#764ba2',
            '#4CAF50',
            '#FF5722',
            '#F8FAFC',
            null,
            null,
            null,
            'Welcome to\nPlumeria Staff',
            'Construction Management Made Simple',
            'Welcome back,',
            'Have a productive day!',
            false,
            false,
            true,
            true,
            nextVersion,
            true
          ]
        );

        await connection.commit();
        connection.release();

        // Return the default branding configuration
        const defaultBranding = {
          appName: 'Plumeria Staff',
          companyName: 'Plumeria Construction',
          tagline: 'Construction Management Made Simple',
          theme: {
            primaryColor: '#667eea',
            secondaryColor: '#764ba2',
            accentColor: '#4CAF50',
            errorColor: '#FF5722',
            backgroundColor: '#F8FAFC'
          },
          assets: {
            logoUrl: null,
            appIconUrl: null,
            splashImageUrl: null
          },
          texts: {
            loginWelcome: 'Welcome to\nPlumeria Staff',
            loginSubtitle: 'Construction Management Made Simple',
            dashboardWelcome: 'Welcome back,',
            dashboardSubtitle: 'Have a productive day!'
          },
          features: {
            showRegistration: false,
            enableSocialLogin: false,
            enableBiometric: true,
            showForgotPassword: true
          },
          version: nextVersion,
          lastUpdated: new Date().toISOString()
        };

        return ResponseUtil.success(res, defaultBranding, 'Branding reset to default successfully');
      } catch (error) {
        await connection.rollback();
        connection.release();
        throw error;
      }
    } catch (error) {
      console.error('Error resetting branding to default:', error);
      return ResponseUtil.error(res, 'Failed to reset branding to default', 500);
    }
  }
}

module.exports = BrandingController;

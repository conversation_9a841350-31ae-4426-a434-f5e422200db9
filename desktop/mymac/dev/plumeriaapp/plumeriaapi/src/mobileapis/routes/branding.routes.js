const express = require('express');
const router = express.Router();
const BrandingController = require('../controllers/branding.controller');

/**
 * Branding Routes for Mobile API
 * Base URL: /api/mobile/v1/branding
 */

/**
 * @route GET /api/mobile/v1/branding
 * @desc Get current branding configuration
 * @access Public
 */
router.get('/', BrandingController.getBranding);

/**
 * @route GET /api/mobile/v1/branding/version
 * @desc Check branding version for cache invalidation
 * @access Public
 */
router.get('/version', BrandingController.checkVersion);

/**
 * @route PUT /api/mobile/v1/branding
 * @desc Update branding configuration (Admin only)
 * @access Private (Admin)
 * @body {
 *   appName: string,
 *   companyName: string,
 *   tagline?: string,
 *   theme?: {
 *     primaryColor?: string,
 *     secondaryColor?: string,
 *     accentColor?: string,
 *     errorColor?: string,
 *     backgroundColor?: string
 *   },
 *   assets?: {
 *     logoUrl?: string,
 *     appIconUrl?: string,
 *     splashImageUrl?: string
 *   },
 *   texts?: {
 *     loginWelcome?: string,
 *     loginSubtitle?: string,
 *     dashboardWelcome?: string,
 *     dashboardSubtitle?: string
 *   },
 *   features?: {
 *     showRegistration?: boolean,
 *     enableSocialLogin?: boolean,
 *     enableBiometric?: boolean,
 *     showForgotPassword?: boolean
 *   }
 * }
 */
router.put('/', (req, res, next) => {
  // Simple admin check - you can enhance this with proper admin middleware
  const adminKey = req.headers['x-admin-key'];
  if (!adminKey || adminKey !== process.env.ADMIN_API_KEY) {
    return res.status(401).json({
      success: false,
      message: 'Unauthorized: Admin access required',
      timestamp: new Date().toISOString()
    });
  }
  next();
}, BrandingController.updateBranding);

/**
 * @route POST /api/mobile/v1/branding/reset
 * @desc Reset branding to default configuration (Admin only)
 * @access Private (Admin)
 */
router.post('/reset', (req, res, next) => {
  // Simple admin check - you can enhance this with proper admin middleware
  const adminKey = req.headers['x-admin-key'];
  if (!adminKey || adminKey !== process.env.ADMIN_API_KEY) {
    return res.status(401).json({
      success: false,
      message: 'Unauthorized: Admin access required',
      timestamp: new Date().toISOString()
    });
  }
  next();
}, BrandingController.resetToDefault);

module.exports = router;

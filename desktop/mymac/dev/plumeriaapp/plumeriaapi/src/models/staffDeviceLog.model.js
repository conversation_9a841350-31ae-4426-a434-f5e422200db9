// src/models/staffDeviceLog.model.js
const db = require('../config/database');

const StaffDeviceLog = {
  /**
   * Log a device event
   */
  logEvent: async (logData) => {
    const {
      staff_id,
      device_id = null,
      device_identifier = null,
      event_type,
      event_description = null,
      ip_address = null,
      user_agent = null,
      platform = null,
      app_version = null,
      location_info = null,
      additional_data = null,
      risk_score = 0
    } = logData;

    const query = `
      INSERT INTO staff_device_logs (
        staff_id, device_id, device_identifier, event_type, event_description,
        ip_address, user_agent, platform, app_version, location_info,
        additional_data, risk_score
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `;

    const result = await db.query(query, [
      staff_id, device_id, device_identifier, event_type, event_description,
      ip_address, user_agent, platform, app_version,
      location_info ? JSON.stringify(location_info) : null,
      additional_data ? JSON.stringify(additional_data) : null,
      risk_score
    ]);

    return result.insertId;
  },

  /**
   * Log successful login
   */
  logLoginSuccess: async (staff_id, device_id, device_identifier, ip_address, user_agent, platform, app_version) => {
    return await StaffDeviceLog.logEvent({
      staff_id,
      device_id,
      device_identifier,
      event_type: 'login_success',
      event_description: 'Staff logged in successfully',
      ip_address,
      user_agent,
      platform,
      app_version,
      risk_score: 0
    });
  },

  /**
   * Log failed login attempt
   */
  logLoginFailed: async (staff_id, device_identifier, reason, ip_address, user_agent, platform, app_version) => {
    const riskScore = StaffDeviceLog.calculateRiskScore('login_failed', { reason, ip_address });
    
    return await StaffDeviceLog.logEvent({
      staff_id,
      device_identifier,
      event_type: 'login_failed',
      event_description: `Failed login attempt: ${reason}`,
      ip_address,
      user_agent,
      platform,
      app_version,
      additional_data: { failure_reason: reason },
      risk_score: riskScore
    });
  },

  /**
   * Log device registration
   */
  logDeviceRegistered: async (staff_id, device_id, device_identifier, ip_address, platform, app_version) => {
    return await StaffDeviceLog.logEvent({
      staff_id,
      device_id,
      device_identifier,
      event_type: 'device_registered',
      event_description: 'New device registered for staff member',
      ip_address,
      platform,
      app_version,
      risk_score: 2 // New device registration has slight risk
    });
  },

  /**
   * Log logout
   */
  logLogout: async (staff_id, device_id, device_identifier, logout_reason, ip_address) => {
    return await StaffDeviceLog.logEvent({
      staff_id,
      device_id,
      device_identifier,
      event_type: 'logout',
      event_description: `Staff logged out: ${logout_reason}`,
      ip_address,
      additional_data: { logout_reason },
      risk_score: 0
    });
  },

  /**
   * Log suspicious activity
   */
  logSuspiciousActivity: async (staff_id, device_identifier, activity_type, details, ip_address, user_agent) => {
    const riskScore = StaffDeviceLog.calculateRiskScore('suspicious_activity', { activity_type, details });
    
    return await StaffDeviceLog.logEvent({
      staff_id,
      device_identifier,
      event_type: 'suspicious_activity',
      event_description: `Suspicious activity detected: ${activity_type}`,
      ip_address,
      user_agent,
      additional_data: { activity_type, details },
      risk_score: riskScore
    });
  },

  /**
   * Get logs for a staff member
   */
  getLogsByStaff: async (staff_id, limit = 50, offset = 0, event_type = null) => {
    let query = `
      SELECT sdl.*, sd.device_name, sd.platform as device_platform
      FROM staff_device_logs sdl
      LEFT JOIN staff_devices sd ON sdl.device_id = sd.id
      WHERE sdl.staff_id = ?
    `;
    
    const params = [staff_id];
    
    if (event_type) {
      query += ' AND sdl.event_type = ?';
      params.push(event_type);
    }
    
    query += ' ORDER BY sdl.created_at DESC LIMIT ? OFFSET ?';
    params.push(limit, offset);
    
    return await db.query(query, params);
  },

  /**
   * Get logs for a specific device
   */
  getLogsByDevice: async (device_id, limit = 50, offset = 0) => {
    const query = `
      SELECT sdl.*, s.staff_name, s.staff_mobile
      FROM staff_device_logs sdl
      LEFT JOIN staffs s ON sdl.staff_id = s.id
      WHERE sdl.device_id = ?
      ORDER BY sdl.created_at DESC
      LIMIT ? OFFSET ?
    `;
    
    return await db.query(query, [device_id, limit, offset]);
  },

  /**
   * Get high-risk events
   */
  getHighRiskEvents: async (risk_threshold = 5, days = 7, limit = 100) => {
    const query = `
      SELECT sdl.*, s.staff_name, s.staff_mobile, sd.device_name
      FROM staff_device_logs sdl
      LEFT JOIN staffs s ON sdl.staff_id = s.id
      LEFT JOIN staff_devices sd ON sdl.device_id = sd.id
      WHERE sdl.risk_score >= ? 
      AND sdl.created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
      ORDER BY sdl.risk_score DESC, sdl.created_at DESC
      LIMIT ?
    `;
    
    return await db.query(query, [risk_threshold, days, limit]);
  },

  /**
   * Get login statistics
   */
  getLoginStats: async (staff_id, days = 30) => {
    const query = `
      SELECT 
        COUNT(CASE WHEN event_type = 'login_success' THEN 1 END) as successful_logins,
        COUNT(CASE WHEN event_type = 'login_failed' THEN 1 END) as failed_logins,
        COUNT(DISTINCT ip_address) as unique_ips,
        COUNT(DISTINCT device_identifier) as unique_devices,
        MAX(CASE WHEN event_type = 'login_success' THEN created_at END) as last_successful_login,
        AVG(risk_score) as avg_risk_score
      FROM staff_device_logs 
      WHERE staff_id = ? 
      AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
      AND event_type IN ('login_success', 'login_failed')
    `;
    
    const stats = await db.query(query, [staff_id, days]);
    return stats[0];
  },

  /**
   * Calculate risk score based on event type and context
   */
  calculateRiskScore: (event_type, context = {}) => {
    let baseScore = 0;
    
    switch (event_type) {
      case 'login_success':
        baseScore = 0;
        break;
      case 'login_failed':
        baseScore = 3;
        if (context.reason && context.reason.includes('locked')) {
          baseScore = 6;
        }
        break;
      case 'device_registered':
        baseScore = 2;
        break;
      case 'suspicious_activity':
        baseScore = 7;
        if (context.activity_type === 'multiple_failed_attempts') {
          baseScore = 9;
        }
        break;
      case 'token_refresh':
        baseScore = 1;
        break;
      default:
        baseScore = 1;
    }
    
    // Additional risk factors
    if (context.ip_address && context.ip_address.startsWith('10.')) {
      baseScore = Math.max(0, baseScore - 1); // Lower risk for internal IPs
    }
    
    return Math.min(10, baseScore); // Cap at 10
  },

  /**
   * Clean up old logs
   */
  cleanupOldLogs: async (days = 180) => {
    const query = `
      DELETE FROM staff_device_logs 
      WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)
      AND risk_score < 5
    `;
    
    return await db.query(query, [days]);
  },

  /**
   * Get event type statistics
   */
  getEventTypeStats: async (days = 30) => {
    const query = `
      SELECT 
        event_type,
        COUNT(*) as event_count,
        AVG(risk_score) as avg_risk_score,
        MAX(created_at) as last_occurrence
      FROM staff_device_logs 
      WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
      GROUP BY event_type
      ORDER BY event_count DESC
    `;
    
    return await db.query(query, [days]);
  }
};

module.exports = StaffDeviceLog;
